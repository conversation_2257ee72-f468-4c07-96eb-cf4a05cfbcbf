{"current_level": 6, "score": 9600, "high_score": 9600, "player_data": {"health": 58, "max_health": 100, "damage": 30, "speed": 7, "fire_rate": 250, "level": 6, "xp": 38, "xp_to_next_level": 757, "upgrade_points": 5, "progression_data": {"skill_tree": {"skill_points": 5, "learned_skills": {}, "active_synergies": []}, "equipment_manager": {"equipped": {"weapon": {"equipment_type": "weapon", "name": "Blade", "rarity": "Uncommon", "level": 1, "stats": {"damage_bonus": 10.88, "fire_rate_bonus": 97.12, "critical_chance": 0.12}}, "armor": {"equipment_type": "armor", "name": "Bulwark", "rarity": "Common", "level": 1, "stats": {"regeneration": 1.14}}, "accessory": {"equipment_type": "accessory", "name": "Crystal", "rarity": "Uncommon", "level": 1, "stats": {"xp_bonus": 0.3, "item_find": 0.24, "skill_cooldown": 0.24}}}, "inventory": [{"equipment_type": "armor", "name": "Fortress", "rarity": "Common", "level": 1, "stats": {"health_bonus": 26.2, "speed_bonus": 0.72, "regeneration": 1.27}}, {"equipment_type": "armor", "name": "Shield", "rarity": "Common", "level": 1, "stats": {"health_bonus": 29.29, "damage_reduction": 0.13, "speed_bonus": 0.7, "regeneration": 1.42}}, {"equipment_type": "armor", "name": "Mail", "rarity": "Common", "level": 1, "stats": {"health_bonus": 35.21, "regeneration": 1.49}}, {"equipment_type": "armor", "name": "Aegis", "rarity": "Common", "level": 1, "stats": {"health_bonus": 25.97, "speed_bonus": 0.68}}, {"equipment_type": "weapon", "name": "Sword", "rarity": "Common", "level": 1, "stats": {"damage_bonus": 8.72}}, {"equipment_type": "weapon", "name": "Rifle", "rarity": "Uncommon", "level": 1, "stats": {"damage_bonus": 9.58, "fire_rate_bonus": 97.9, "critical_chance": 0.11, "projectile_speed": 3.68}}]}, "achievement_manager": {"achievements": {"first_steps": {"name": "First Steps", "unlocked": true, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "experienced": {"name": "Experienced", "unlocked": true, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "veteran": {"name": "Veteran", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "master": {"name": "Master", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "first_blood": {"name": "First Blood", "unlocked": true, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "slayer": {"name": "Slayer", "unlocked": true, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "destroyer": {"name": "Destroyer", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "boss_hunter": {"name": "<PERSON>", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "boss_slayer": {"name": "Boss Slayer", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "survivor": {"name": "Survivor", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "untouchable": {"name": "Untouchable", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "iron_will": {"name": "Iron Will", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "skill_student": {"name": "Skill Student", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "skill_master": {"name": "Skill Master", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "well_equipped": {"name": "Well Equipped", "unlocked": true, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "fully_equipped": {"name": "Fully Equipped", "unlocked": true, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "collector": {"name": "Collector", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "hoarder": {"name": "Hoarder", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "speed_runner": {"name": "Speed Runner", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "perfectionist": {"name": "Perfectionist", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "secret_finder": {"name": "Secret Finder", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "lucky_shot": {"name": "Lucky Shot", "unlocked": false, "progress": 0, "achievement_type": "simple", "max_progress": 1}, "enemy_slayer": {"name": "Enemy Slayer", "unlocked": false, "progress": 56, "achievement_type": "progressive", "max_progress": 100}, "damage_dealer": {"name": "Damage Dealer", "unlocked": false, "progress": 0, "achievement_type": "progressive", "max_progress": 10000}, "treasure_hunter": {"name": "Treasure Hunter", "unlocked": false, "progress": 9, "achievement_type": "progressive", "max_progress": 50}, "combat_master": {"name": "Combat Master", "unlocked": false, "progress": 0, "achievement_type": "chain", "max_progress": 1}, "immortal": {"name": "<PERSON><PERSON><PERSON><PERSON>", "unlocked": false, "progress": 0, "achievement_type": "chain", "max_progress": 1}, "master_explorer": {"name": "Master Explorer", "unlocked": false, "progress": 0, "achievement_type": "chain", "max_progress": 1}}, "completed_chains": []}, "stats": {"enemies_killed": 56, "bosses_killed": 0, "levels_completed": 0, "perfect_levels": 0, "near_death_survivals": 0, "skills_learned": 0, "maxed_skills": 0, "maxed_combat_skills": 0, "equipment_equipped": 9, "full_equipment_sets": 4, "items_collected": 9, "secrets_found": 0, "max_crit_streak": 2, "current_crit_streak": 0, "fastest_level_time": Infinity, "player_level": 6, "total_damage_dealt": 0}, "regen_timer": 22}}}